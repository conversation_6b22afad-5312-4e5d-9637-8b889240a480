#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import os
import re
import requests
import json
import tempfile
from urllib.parse import urlencode
from typing import Dict, List, Optional, Union

from telegram import Update, Chat, Message, InputFile
from telegram.ext import Application, CommandHandler, MessageHandler, ContextTypes, filters

# Enable logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configuration
TOKEN = os.environ.get("7551546616:AAGA_s4GqFA4mdVG0kM_z7WVZs0NvwpSyng", "7551546616:AAGA_s4GqFA4mdVG0kM_z7WVZs0NvwpSyng")
ALLOWED_GROUPS = [
    -1002279433889,  # Example group ID 1
    -1009876543210,  # Example group ID 2
    # Add more group IDs as needed
]

# Allowed topics within groups (by name)
ALLOWED_TOPICS = [
    "Shader",
    "test",
    # Add more allowed topic names as needed
]

# Zepeto API Configuration
ZEPETO_SPACE_ID = "ttn7jks47ozr"
ZEPETO_ENVIRONMENT = "master"
ZEPETO_TOKEN = "CFDAK-MlHrg3wK88bsyLdK3n3f66766d856f226f8dec3WiAxE"

class TelegramBot:
    def __init__(self, token: str, allowed_groups: List[int], allowed_topics: List[str]):
        """Initialize the bot with token, allowed groups, and allowed topics."""
        self.token = token
        self.allowed_groups = allowed_groups
        self.allowed_topics = [topic.lower() for topic in allowed_topics]  # Convert to lowercase for case-insensitive comparison
        
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send a message when the command /start is issued."""
        user = update.effective_user
        chat_type = update.effective_chat.type
        chat_id = update.effective_chat.id
        
        # Check if message is in a topic
        message_thread_id = update.effective_message.message_thread_id if update.effective_message else None
        topic_name = self._get_topic_name(update)
        
        # Log the group ID and topic info
        if chat_type in ["group", "supergroup"]:
            if message_thread_id:
                logger.info(f"Received /start command in group: {chat_id}, topic ID: {message_thread_id}, topic name: {topic_name}")
            else:
                logger.info(f"Received /start command in group: {chat_id}, not in a topic")
            
            # Check if group is allowed and topic is allowed
            if chat_id in self.allowed_groups:
                if self._is_allowed_topic(update):
                    await update.message.reply_text(
                        f"Hello {user.first_name}! This group and topic are allowed. Bot is active."
                    )
                else:
                    await update.message.reply_text(
                        f"Hello {user.first_name}! This group is allowed, but I only respond in specific topics like 'general'."
                    )
            else:
                await update.message.reply_text(
                    "This group is not whitelisted. Bot functionality is limited."
                )
        else:
            logger.info(f"Received /start command in private chat: {chat_id}")
            await update.message.reply_text(
                "This bot is designed to work in whitelisted groups and specific topics only."
            )

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send a message when the command /help is issued."""
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type
        
        # Log the group ID and topic info
        message_thread_id = update.effective_message.message_thread_id if update.effective_message else None
        topic_name = self._get_topic_name(update)
        
        if chat_type in ["group", "supergroup"]:
            if message_thread_id:
                logger.info(f"Received /help command in group: {chat_id}, topic ID: {message_thread_id}, topic name: {topic_name}")
            else:
                logger.info(f"Received /help command in group: {chat_id}, not in a topic")
        
        # Only respond if in allowed group and topic
        if self._is_whitelisted(chat_id) and self._is_allowed_topic(update):
            help_text = (
                "Available commands:\n"
                "/start - Start the bot\n"
                "/help - Show this help message\n"
                "/shader <URL or ID> - Process a shader with URL or ID\n"
                "Examples:\n"
                "/shader https://web.zepeto.me/share/contents/preview/CR_678284867c944f2b729b9854\n"
                "/shader CR_678284867c944f2b729b9854"
            )
            await update.message.reply_text(help_text)
        elif self._is_whitelisted(chat_id):
            await update.message.reply_text(
                "I only respond in specific topics like 'general'. Please use me there."
            )
        else:
            help_text = (
                "This group is not whitelisted.\n"
                "Available commands are limited to:\n"
                "/start - Start the bot\n"
                "/help - Show this help message"
            )
            await update.message.reply_text(help_text)

    async def shader_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Process shader command with URL or ID parameter."""
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type
        
        # Log the group ID and topic info
        message_thread_id = update.effective_message.message_thread_id if update.effective_message else None
        topic_name = self._get_topic_name(update)
        
        if chat_type in ["group", "supergroup"]:
            if message_thread_id:
                logger.info(f"Received /shader command in group: {chat_id}, topic ID: {message_thread_id}, topic name: {topic_name}")
            else:
                logger.info(f"Received /shader command in group: {chat_id}, not in a topic")
        
        # Only respond if in allowed group and topic
        if not (self._is_whitelisted(chat_id) and self._is_allowed_topic(update)):
            if self._is_whitelisted(chat_id):
                await update.message.reply_text(
                    "I only respond in specific topics like 'general'. Please use me there."
                )
            else:
                await update.message.reply_text("This group is not whitelisted.")
            return
        
        # Check if there are any arguments
        if not context.args:
            await update.message.reply_text(
                "Please provide a URL or ID.\n"
                "Examples:\n"
                "/shader https://web.zepeto.me/share/contents/preview/CR_678284867c944f2b729b9854\n"
                "/shader CR_678284867c944f2b729b9854"
            )
            return
        
        # Get the argument (URL or ID)
        arg = context.args[0]
        
        # Extract the ID from the URL or use the provided ID directly
        shader_id = self._extract_shader_id(arg)
        
        if shader_id:
            # Send a message indicating processing has started
            processing_message = await update.message.reply_text(f"Processing shader with ID: {shader_id}...")
            
            try:
                # Process the shader and get the file
                file_path, file_name = await self._process_shader(shader_id)
                
                if file_path:
                    # Delete the processing message and send a new one with the file
                    # Note: Telegram doesn't support editing a message to add a file, so we need to delete and send a new one
                    await processing_message.delete()
                    
                    # Send the success message with the file
                    with open(file_path, 'rb') as file:
                        await context.bot.send_document(
                            chat_id=update.effective_chat.id,
                            document=file,
                            filename=file_name,
                            caption=f"✅ Shader with ID: {shader_id} processed successfully.",
                            reply_to_message_id=update.message.message_id,
                            message_thread_id=message_thread_id
                        )
                    
                    # Delete the temporary file
                    os.remove(file_path)
                    logger.info(f"File sent and deleted: {file_path}")
                else:
                    await processing_message.edit_text(f"❌ Failed to process shader with ID: {shader_id}")
            except Exception as e:
                logger.error(f"Error processing shader {shader_id}: {str(e)}")
                await processing_message.edit_text(f"❌ Error processing shader: {str(e)}")
        else:
            await update.message.reply_text(
                "Invalid URL or ID format. Please use one of these formats:\n"
                "- https://web.zepeto.me/share/contents/preview/CR_XXXXXXXXXXXXXXXXXXXX\n"
                "- CR_XXXXXXXXXXXXXXXXXXXX"
            )

    async def _process_shader(self, shader_id: str) -> tuple[Optional[str], Optional[str]]:
        """Process a shader ID and return the file path and name."""
        try:
            # Extract the code from shader_id (remove CR_ prefix)
            code = shader_id
            if shader_id.startswith("CR_"):
                code = shader_id[0:]  # Remove CR_ prefix
            
            # Build entries URL
            url = self._build_entries_url(code, ZEPETO_SPACE_ID, ZEPETO_ENVIRONMENT)
            
            # Send request with headers
            headers = {
                "Authorization": f"Bearer {ZEPETO_TOKEN}"
            }
            
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                logger.error(f"Error fetching entries: {response.status_code} - {response.text}")
                return None, None
            
            response_json = response.json()
            
            # Check if we have items and resource
            if not response_json.get("items") or not response_json["items"][0].get("fields") or not response_json["items"][0]["fields"].get("resource"):
                logger.error(f"No resource found for shader ID: {shader_id}")
                return None, None
            
            # Get asset ID
            asset_id = response_json["items"][0]["fields"]["resource"]["sys"]["id"]
            
            # Build and fetch asset URL
            asset_url = self._build_asset_url(asset_id, ZEPETO_SPACE_ID, ZEPETO_ENVIRONMENT)
            asset_response = requests.get(asset_url, headers=headers)
            
            if asset_response.status_code != 200:
                logger.error(f"Error fetching asset: {asset_response.status_code} - {asset_response.text}")
                return None, None
            
            asset_json = asset_response.json()
            
            # Get file URL
            if not asset_json.get("fields") or not asset_json["fields"].get("file") or not asset_json["fields"]["file"].get("en"):
                logger.error(f"No file URL found for asset ID: {asset_id}")
                return None, None
            
            file_url = asset_json["fields"]["file"]["en"]["url"]
            file_name = asset_json["fields"]["file"]["en"]["fileName"]
            
            # Download the file to a temporary location
            file_response = requests.get(file_url)
            if file_response.status_code != 200:
                logger.error(f"Error downloading file: {file_response.status_code}")
                return None, None
            
            # Create a temporary file
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, file_name)
            
            with open(temp_file_path, "wb") as f:
                f.write(file_response.content)
            
            logger.info(f"File downloaded to: {temp_file_path}")
            return temp_file_path, file_name
            
        except Exception as e:
            logger.error(f"Error in _process_shader: {str(e)}")
            return None, None

    def _build_entries_url(self, code: str, space_id: str, environment: str) -> str:
        """Build the entries URL for the Zepeto API."""
        params = {
            "content_type": "contents",
            "fields.code": code,
            "limit": "1"
        }
        query_string = urlencode(params)
        url = f"https://cdn-zepetoful.zepeto.io/spaces/{space_id}/environments/{environment}/entries?{query_string}"
        return url

    def _build_asset_url(self, asset_id: str, space_id: str, environment: str) -> str:
        """Build the asset URL for the Zepeto API."""
        return f"https://cdn-zepetoful.zepeto.io/spaces/{space_id}/environments/{environment}/assets/{asset_id}"

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle incoming messages."""
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type
        
        # Check if message is in a topic
        message_thread_id = update.effective_message.message_thread_id if update.effective_message else None
        topic_name = self._get_topic_name(update)
        
        # Log every message with group ID and topic info
        if chat_type in ["group", "supergroup"]:
            if message_thread_id:
                logger.info(f"Received message in group: {chat_id}, topic ID: {message_thread_id}, topic name: {topic_name}")
            else:
                logger.info(f"Received message in group: {chat_id}, not in a topic")
            
            # Only process messages from whitelisted groups and allowed topics
            if self._is_whitelisted(chat_id) and self._is_allowed_topic(update):
                # Process message for whitelisted group and allowed topic
                await self._process_whitelisted_message(update, context)
            elif self._is_whitelisted(chat_id):
                # Group is whitelisted but topic is not allowed
                logger.info(f"Message from whitelisted group but not in allowed topic: {chat_id}")
            else:
                # Group is not whitelisted
                logger.info(f"Message from non-whitelisted group: {chat_id}")
    
    async def _process_whitelisted_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Process messages from whitelisted groups and allowed topics."""
        message = update.message.text.lower()
        chat_id = update.effective_chat.id
        
        # Example: Respond to specific keywords
        if "hello bot" in message:
            await update.message.reply_text("Hello! I'm active in this topic.")
        
        # Log message content for debugging
        logger.info(f"Processing message in whitelisted group {chat_id}: '{update.message.text[:20]}...'")
        
        # Add more message processing logic here
    
    def _is_whitelisted(self, chat_id: int) -> bool:
        """Check if a chat ID is in the whitelist."""
        return chat_id in self.allowed_groups
    
    def _get_topic_name(self, update: Update) -> Optional[str]:
        """Try to get the topic name from the update."""
        # This is a simplified approach - in a real bot, you might need to store topic IDs and names
        # or use additional API calls to get the topic name from message_thread_id
        
        # Check if the message has a thread_id (is in a topic)
        if update.effective_message and update.effective_message.message_thread_id:
            # In a real implementation, you would map thread_id to topic name
            # For now, we'll check if the message has a reply to topic or forum topic
            if update.effective_message.forum_topic_created:
                return update.effective_message.forum_topic_created.name
            elif update.effective_message.reply_to_message and update.effective_message.reply_to_message.forum_topic_created:
                return update.effective_message.reply_to_message.forum_topic_created.name
            
            # If we can't determine the name, we'll use the thread ID as a string
            return f"topic_{update.effective_message.message_thread_id}"
        
        return None
    
    def _is_allowed_topic(self, update: Update) -> bool:
        """Check if the message is in an allowed topic."""
        # If no topics are specified, allow all
        if not self.allowed_topics:
            return True
            
        # If the message is not in a topic (main group chat), don't allow
        topic_name = self._get_topic_name(update)
        if not topic_name:
            return False
            
        # Check if the topic name is in our allowed list
        # We do a partial match to be more flexible
        topic_name_lower = topic_name.lower()
        for allowed_topic in self.allowed_topics:
            if allowed_topic in topic_name_lower:
                return True
                
        return False
    
    def _extract_shader_id(self, text: str) -> Optional[str]:
        """Extract shader ID from URL or direct ID."""
        # Check if it's a direct ID (starts with CR_)
        if text.startswith("CR_"):
            return text
        
        # Check if it's a URL
        url_pattern = r"https://web\.zepeto\.me/share/contents/preview/(CR_[a-zA-Z0-9]+)"
        match = re.search(url_pattern, text)
        if match:
            return match.group(1)
        
        return None

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors."""
        logger.error(f"Error occurred: {context.error}")
    
    def run(self) -> None:
        """Run the bot."""
        # Create the Application
        application = Application.builder().token(self.token).build()

        # Add command handlers
        application.add_handler(CommandHandler("start", self.start))
        application.add_handler(CommandHandler("help", self.help_command))
        application.add_handler(CommandHandler("shader", self.shader_command))
        
        # Add message handler
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        # Add error handler
        application.add_error_handler(self.error_handler)

        # Run the bot
        logger.info("Starting bot...")
        application.run_polling()


def main() -> None:
    """Start the bot."""
    # Create and run the bot
    logger.info("Initializing bot with token, allowed groups, and allowed topics")
    bot = TelegramBot(TOKEN, ALLOWED_GROUPS, ALLOWED_TOPICS)
    bot.run()


if __name__ == "__main__":
    main()
