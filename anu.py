import requests
from urllib.parse import urlencode
import json
import os

def build_entries_url(code: str, space_id: str, environment: str) -> str:
    params = {
        "content_type": "contents",
        "fields.code": code,
        "limit": "1"
    }
    query_string = urlencode(params)
    url = f"https://cdn-zepetoful.zepeto.io/spaces/{space_id}/environments/{environment}/entries?{query_string}"
    return url

def build_asset_url(asset_id: str, space_id: str, environment: str) -> str:
    return f"https://cdn-zepetoful.zepeto.io/spaces/{space_id}/environments/{environment}/assets/{asset_id}"

# Contoh penggunaan
space_id = "ttn7jks47ozr"
environment = "master"
code = input("Masukkan code: ")

# Bearer token
token = "CFDAK-MlHrg3wK88bsyLdK3n3f66766d856f226f8dec3WiAxE"

# Build URL
url = build_entries_url(code, space_id, environment)

# Kirim request GET dengan header Authorization
headers = {
    "Authorization": f"Bearer {token}"
}

response = requests.get(url, headers=headers)

# Tampilkan hasil response entries
# print("Status Code (Entries):", response.status_code)

try:
    response_json = response.json()
    pretty_json = json.dumps(response_json, indent=4, ensure_ascii=False)
    # print("Response JSON (Entries):\n", pretty_json)
    
    # Get resource asset ID from the response
    if response_json["items"] and "resource" in response_json["items"][0]["fields"]:
        asset_id = response_json["items"][0]["fields"]["resource"]["sys"]["id"]
        
        # Build and fetch asset URL
        asset_url = build_asset_url(asset_id, space_id, environment)
        # print("\nAsset URL:", asset_url)
        asset_headers = {
            "Authorization": f"Bearer {token}"
        }
        asset_response = requests.get(asset_url, headers=asset_headers)
        
        # print("\nStatus Code (Asset):", asset_response.status_code)
        try:
            asset_json = asset_response.json()
            # Ambil hanya file url
            file_url = asset_json["fields"]["file"]["en"]["url"]
            print("File URL:", file_url)
            # Download the file and save to /downloads
            download_dir = os.path.join(os.path.dirname(__file__), "downloads")
            os.makedirs(download_dir, exist_ok=True)
            file_name = asset_json["fields"]["file"]["en"]["fileName"]
            file_path = os.path.join(download_dir, file_name)
            base, ext = os.path.splitext(file_name)
            counter = 1
            # Tambahkan angka unik jika file sudah ada
            while os.path.exists(file_path):
                file_path = os.path.join(download_dir, f"{base}_{counter}{ext}")
                counter += 1
            file_response = requests.get(file_url)
            if file_response.status_code == 200:
                with open(file_path, "wb") as f:
                    f.write(file_response.content)
                print(f"File downloaded and saved to: {file_path}")
            else:
                print(f"Failed to download file. Status code: {file_response.status_code}")
        except ValueError:
            print("Gagal parsing JSON Asset:\n", asset_response.text)
    else:
        print("Resource ID not found in the response")
        
except ValueError:
    print("Gagal parsing JSON Entries:\n", response.text)
