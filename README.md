# Telegram Group Bot with Whitelist

A Python Telegram bot specifically designed to work with groups and implement a whitelist system.

## Features

- Group detection and identification
- Whitelist system for allowed groups
- Different functionality for whitelisted vs non-whitelisted groups
- Basic commands: `/start`, `/help`, `/status`, `/info`
- Message handling for whitelisted groups

## Requirements

- Python 3.7+
- python-telegram-bot v20.0+

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd <repository-directory>
```

2. Install required packages:
```bash
pip install python-telegram-bot
```

## Configuration

1. Get a bot token from [BotFather](https://t.me/BotFather)
2. Set up your bot token and whitelist:
   - Option 1: Edit the `bot_telegram.py` file directly
   - Option 2: Set environment variables:
     ```bash
     export TELEGRAM_BOT_TOKEN="your_bot_token_here"
     ```

3. Add your whitelisted group IDs to the `ALLOWED_GROUPS` list in `bot_telegram.py`

## How to Find Group IDs

To get a Telegram group ID:
1. Add your bot to the group
2. Send a message in the group
3. Visit `https://api.telegram.org/bot<YourBOTToken>/getUpdates`
4. Look for the `"chat":{"id":` field in the JSON response
5. Note: Group IDs are typically negative numbers

## Running the Bot

```bash
python bot_telegram.py
```

## Commands

- `/start` - Start the bot
- `/help` - Show help message
- `/status` - Check if the current group is whitelisted
- `/info` - Get information about the current group (whitelisted groups only)

## Customizing the Bot

To add more functionality, you can modify the `_process_whitelisted_message` method in the `TelegramBot` class.

## License

[MIT License](LICENSE) 